using System.Diagnostics;
using Microsoft.Extensions.Logging;
using WinFormsAppIssueTime.Interfaces;
using WinFormsAppIssueTime.Models;

namespace WinFormsAppIssueTime;

/// <summary>
/// 主窗体类
/// 负责显示时间发放信息和用户交互
/// </summary>
public partial class FormMain : Form
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<FormMain> _logger;

    /// <summary>
    /// 时间发放服务实例
    /// </summary>
    private readonly IIssueTimeService _issueTimeService;

    /// <summary>
    /// 取消令牌源，用于控制后台任务的取消
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource;

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 构造函数，通过依赖注入获取服务
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="issueTimeService">时间发放服务实例</param>
    /// <exception cref="ArgumentNullException">当服务实例为空时抛出</exception>
    public FormMain(ILogger<FormMain> logger, IIssueTimeService issueTimeService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        InitializeComponent();
        InitializeFormSettings();
        _logger.LogInformation("主窗体已初始化");
    }

    /// <summary>
    /// 初始化窗体设置
    /// </summary>
    private void InitializeFormSettings()
    {
        // 设置窗体属性
        StartPosition = FormStartPosition.CenterScreen; // 居中显示
        FormBorderStyle = FormBorderStyle.FixedSingle; // 固定边框
        MaximizeBox = false; // 禁用最大化按钮
        MinimizeBox = true; // 启用最小化按钮

        // 初始化取消令牌
        _cancellationTokenSource = new CancellationTokenSource();
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 窗体加载事件处理程序
    /// 初始化数据并启动实时更新任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            // 显示加载状态
            UpdateStatusDisplay("正在初始化数据...", Color.Blue);

            // 创建发放时间数据
            await _issueTimeService.CreateIssueTimeAsync();

            // 获取数据统计信息
            await DisplayDataStatistics();

            // 启动实时更新任务
            _ = Task.Run(() => StartRealTimeUpdateAsync(_cancellationTokenSource!.Token));

            UpdateStatusDisplay("系统运行中", Color.Green);
        }
        catch (Exception ex)
        {
            var errorMessage = $"系统初始化失败: {ex.Message}";
            UpdateStatusDisplay(errorMessage, Color.Red);
            MessageBox.Show(errorMessage, "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 窗体关闭事件处理程序
    /// 清理资源并取消后台任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        // 清理自定义资源
        CleanupResources();
    }

    #endregion

    #region 业务逻辑方法

    /// <summary>
    /// 显示数据统计信息
    /// </summary>
    private async Task DisplayDataStatistics()
    {
        try
        {
            var totalCount = await _issueTimeService.GetIssueTimeCountAsync();
            var firstRecord = totalCount > 0 ? await _issueTimeService.GetFirstIssueTimeAsync() : null;

            var statisticsMessage = $"数据统计信息:\n" +
                                    $"总记录数: {totalCount:N0} 条\n" +
                                    $"数据年份: {firstRecord?.OpenTime.Year ?? DateTime.Now.Year}\n" +
                                    $"系统状态: 就绪";

            Debug.WriteLine(statisticsMessage);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"获取统计信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 启动实时更新任务
    /// 持续监控和更新发放时间信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task StartRealTimeUpdateAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await UpdateIssueTimeDisplayAsync();
                await Task.Delay(1000, cancellationToken); // 每秒更新一次
            }
        }
        catch (OperationCanceledException)
        {
            Debug.WriteLine("实时更新任务已取消");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"实时更新任务发生错误: {ex.Message}");

            // 在UI线程上显示错误信息
            if (!cancellationToken.IsCancellationRequested)
            {
                Invoke(() => UpdateStatusDisplay($"更新错误: {ex.Message}", Color.Red));
            }
        }
    }

    /// <summary>
    /// 更新发放时间显示信息
    /// </summary>
    private async Task UpdateIssueTimeDisplayAsync()
    {
        try
        {
            // 获取当前或下一个发放时间段
            var issueTime = await _issueTimeService.GetCurrentOrNextIssueTimeAsync();

            if (issueTime == null)
            {
                Invoke(() => UpdateMainDisplay("没有找到发放时间数据\n可能需要重新生成年度数据", Color.Orange));
                return;
            }

            // 构建显示信息
            var displayInfo = BuildDisplayMessage(issueTime);

            // 确定显示颜色
            var displayColor = GetDisplayColor(issueTime);

            // 在UI线程上更新显示
            Invoke(() => UpdateMainDisplay(displayInfo, displayColor));
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"更新显示信息时发生错误: {ex.Message}");
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 构建显示消息
    /// </summary>
    /// <param name="issueTime">发放时间对象</param>
    /// <returns>格式化的显示消息</returns>
    private string BuildDisplayMessage(IssueTime issueTime)
    {
        var now = DateTime.Now;
        var message = $"发放编号: {issueTime.Issue}\n" +
                      $"开放时间: {issueTime.OpenTime:yyyy-MM-dd HH:mm:ss}\n" +
                      $"关闭时间: {issueTime.CloseTime:yyyy-MM-dd HH:mm:ss}\n" +
                      $"当前时间: {now:yyyy-MM-dd HH:mm:ss}\n";

        // 计算时间差
        var openTimeSpan = issueTime.OpenTime - now;
        var closeTimeSpan = issueTime.CloseTime - now;

        if (openTimeSpan.TotalSeconds > 0)
        {
            // 还未开放
            message += $"\n状态: 等待开放\n";
            message += FormatTimeRemaining("距离开放", openTimeSpan);
        }
        else if (closeTimeSpan.TotalSeconds > 0)
        {
            // 正在开放中
            message += $"\n状态: 正在开放\n";
            message += FormatTimeRemaining("距离关闭", closeTimeSpan);
        }
        else
        {
            // 已关闭，显示下一个时间段
            message += $"\n状态: 已关闭";
        }

        return message;
    }

    /// <summary>
    /// 格式化剩余时间显示
    /// </summary>
    /// <param name="prefix">前缀文本</param>
    /// <param name="timeSpan">时间间隔</param>
    /// <returns>格式化的时间字符串</returns>
    private string FormatTimeRemaining(string prefix, TimeSpan timeSpan)
    {
        var totalSeconds = (int)timeSpan.TotalSeconds;

        if (totalSeconds <= 0)
        {
            return $"{prefix}: 已到时间";
        }

        if (totalSeconds >= 3600) // 大于等于1小时
        {
            var hours = totalSeconds / 3600;
            var minutes = (totalSeconds % 3600) / 60;
            var seconds = totalSeconds % 60;
            return $"{prefix}: {hours} 小时 {minutes} 分 {seconds} 秒";
        }
        else if (totalSeconds >= 60) // 大于等于1分钟
        {
            var minutes = totalSeconds / 60;
            var seconds = totalSeconds % 60;
            return $"{prefix}: {minutes} 分 {seconds} 秒";
        }
        else // 小于1分钟
        {
            return $"{prefix}: {totalSeconds} 秒";
        }
    }

    /// <summary>
    /// 获取显示颜色
    /// </summary>
    /// <param name="issueTime">发放时间对象</param>
    /// <returns>显示颜色</returns>
    private Color GetDisplayColor(IssueTime issueTime)
    {
        var now = DateTime.Now;

        if (now >= issueTime.OpenTime && now <= issueTime.CloseTime)
        {
            return Color.Green; // 正在开放 - 绿色
        }
        else if (now < issueTime.OpenTime)
        {
            var timeToOpen = issueTime.OpenTime - now;
            if (timeToOpen.TotalMinutes <= 5) // 5分钟内即将开放
            {
                return Color.Orange; // 即将开放 - 橙色
            }

            return Color.Blue; // 等待开放 - 蓝色
        }
        else
        {
            return Color.Gray; // 已关闭 - 灰色
        }
    }

    /// <summary>
    /// 更新主显示区域
    /// </summary>
    /// <param name="message">显示消息</param>
    /// <param name="color">显示颜色</param>
    private void UpdateMainDisplay(string message, Color color)
    {
        if (label1 != null)
        {
            label1.Text = message;
            label1.ForeColor = color;
        }
    }

    /// <summary>
    /// 更新状态显示
    /// </summary>
    /// <param name="status">状态消息</param>
    /// <param name="color">显示颜色</param>
    private void UpdateStatusDisplay(string status, Color color)
    {
        // 这里可以添加状态栏或其他状态显示控件的更新
        Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] {status}");
        Debug.WriteLine(color);
        // 如果有状态标签，可以在这里更新
        // if (statusLabel != null)
        // {
        //     statusLabel.Text = status;
        //     statusLabel.ForeColor = color;
        // }
    }

    #endregion

    #region 资源清理

    /// <summary>
    /// 清理自定义资源
    /// 在窗体关闭时调用，用于清理取消令牌等资源
    /// </summary>
    private void CleanupResources()
    {
        try
        {
            // 取消并释放取消令牌源
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"清理资源时发生错误: {ex.Message}");
        }
    }

    #endregion
}