﻿using FreeSql.DataAnnotations;

namespace WinFormsAppIssueTime.Models;

/// <summary>
/// 发放时间实体类
/// 表示一个时间发放的时间段信息
/// </summary>
[Table(Name = "IssueTime")]
public class IssueTime
{
    /// <summary>
    /// 主键ID，自增长
    /// </summary>
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 发放编号，格式：民国年份+6位序号（如：113000001）
    /// </summary>
    [Column(StringLength = 50)]
    public string Issue { get; set; } = string.Empty;

    /// <summary>
    /// 发放开放时间
    /// </summary>
    public DateTime OpenTime { get; set; }

    /// <summary>
    /// 发放关闭时间
    /// </summary>
    public DateTime CloseTime { get; set; }

    /// <summary>
    /// 记录创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}