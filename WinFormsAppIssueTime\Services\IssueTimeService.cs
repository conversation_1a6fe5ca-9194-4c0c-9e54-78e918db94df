﻿using System.Diagnostics;
using WinFormsAppIssueTime.Interfaces;
using WinFormsAppIssueTime.Models;

namespace WinFormsAppIssueTime.Services;

/// <summary>
/// 时间发放服务实现类
/// 负责处理发放时间的业务逻辑，包括数据的创建、查询、删除等操作
/// </summary>
public class IssueTimeService : IIssueTimeService
{
    /// <summary>
    /// FreeSql ORM 实例，用于数据库操作
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 构造函数，通过依赖注入获取 FreeSql 实例
    /// </summary>
    /// <param name="freeSql">FreeSql 实例</param>
    public IssueTimeService(IFreeSql freeSql)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
    }

    /// <summary>
    /// 获取发放记录的总条数
    /// </summary>
    /// <returns>数据库中发放记录的总数量</returns>
    public Task<long> GetIssueTimeCountAsync()
    {
        // 使用 FreeSql 查询 IssueTime 表的记录总数
        return _freeSql.Select<IssueTime>().CountAsync();
    }

    /// <summary>
    /// 获取发放记录的第一条
    /// 用于检查数据库中是否存在数据以及获取年份信息
    /// </summary>
    /// <returns>第一条发放记录</returns>
    public async Task<IssueTime> GetFirstIssueTimeAsync()
    {
        // 获取数据库中的第一条发放记录
        return await _freeSql.Select<IssueTime>().FirstAsync();
    }

    /// <summary>
    /// 创建发放记录
    /// 生成一整年的发放时间数据，每天203次，每次间隔5分钟
    /// </summary>
    public async Task CreateIssueTimeAsync()
    {
        // 判断是否已经有发放记录
        long issueTimeCount = await GetIssueTimeCountAsync();
        if (issueTimeCount > 0)
        {
            // 获取第一条记录来检查年份
            IssueTime issueTime = await GetFirstIssueTimeAsync();

            // 如果当前年份已经有发放记录，则不再创建（避免重复生成）
            if (issueTime.OpenTime.Year == DateTime.Now.Year)
            {
                return;
            }

            // 否则先清空旧数据（跨年时更新数据）
            await ClearIssueTimeAsync();
        }

        // 开始创建发放记录
        int index = 0; // 全局序号计数器

        // 从1月到12月遍历整年
        for (int month = 1; month <= 12; month++)
        {
            // 获取当前月份的天数（自动处理闰年二月）
            int daysInMonth = DateTime.DaysInMonth(DateTime.Now.Year, month);

            // 遍历该月的每一天
            for (int day = 1; day <= daysInMonth; day++)
            {
                // 每天从6:55开始（7:00-5分钟）
                DateTime time = new DateTime(DateTime.Now.Year, month, day, 7, 0, 0).AddMinutes(-5);
                List<IssueTime> issueTimes = new List<IssueTime>(); // 当天的发放时间列表

                // 每天发放203次，每次间隔5分钟
                for (int i = 1; i <= 203; i++)
                {
                    DateTime openTime = time.AddMinutes(5); // 开放时间
                    DateTime closeTime = openTime.AddMinutes(5).AddSeconds(-10); // 关闭时间（开放后5分钟, 关闭前10秒）
                    index++; // 全局序号递增

                    // 生成6位序号（左侧补0）
                    string indexStr = index.ToString().PadLeft(6, '0');
                    // 生成发放编号：民国年份+6位序号
                    string issueTime = $"{DateTime.Now.Year - 1911}{indexStr}";

                    // 创建发放时间对象
                    IssueTime issueTimeModel = new IssueTime()
                    {
                        Issue = issueTime,
                        OpenTime = openTime,
                        CloseTime = closeTime
                    };
                    issueTimes.Add(issueTimeModel);

                    // 顺延时间到下一个发放时间点
                    time = openTime;
                }

                // 调用FreeSql批量插入当天的发放记录
                if (issueTimes.Count > 0)
                {
                    await _freeSql.Insert(issueTimes).ExecuteAffrowsAsync();
                    // Debug.WriteLine($"已插入 {issueTimes.Count} 条记录到数据库 - {DateTime.Now.Year}年{month}月{day}日");
                }
            }
        }

        // 计算并输出统计信息
        int daysInYear = DateTime.IsLeapYear(DateTime.Now.Year) ? 366 : 365; // 判断闰年
        Debug.WriteLine($"{DateTime.Now.Year}年共有{daysInYear}天，总计{index}次发放。");
        await Task.Delay(0); // 异步方法的占位符
    }

    /// <summary>
    /// 清空发放记录
    /// 删除数据库中所有的发放时间记录
    /// </summary>
    /// <returns>删除操作的异步任务</returns>
    public Task ClearIssueTimeAsync()
    {
        // 删除 IssueTime 表中的所有记录
        return _freeSql.Delete<IssueTime>().ExecuteAffrowsAsync();
    }

    /// <summary>
    /// 获取当前时间对应的发放时间段，如果当前时间不在任何时间段内，则返回最近的未来时间段
    /// </summary>
    /// <returns>当前或下一个发放时间段</returns>
    public async Task<IssueTime?> GetCurrentOrNextIssueTimeAsync()
    {
        DateTime now = DateTime.Now;

        // 首先查找当前时间是否在某个发放时间段内（OpenTime <= now <= CloseTime）
        var currentIssueTime = await _freeSql.Select<IssueTime>()
            .Where(x => x.OpenTime <= now && x.CloseTime >= now)
            .FirstAsync();

        if (currentIssueTime != null)
        {
            Debug.WriteLine($"当前时间 {now:yyyy-MM-dd HH:mm:ss} 属于发放时间段: {currentIssueTime.Issue} ({currentIssueTime.OpenTime:yyyy-MM-dd HH:mm:ss} - {currentIssueTime.CloseTime:yyyy-MM-dd HH:mm:ss})");
            return currentIssueTime;
        }

        // 如果当前时间不在任何发放时间段内，查找最近的未来时间段
        var nextIssueTime = await _freeSql.Select<IssueTime>()
            .Where(x => x.OpenTime > now)
            .OrderBy(x => x.OpenTime)
            .FirstAsync();

        if (nextIssueTime != null)
        {
            Debug.WriteLine($"当前时间 {now:yyyy-MM-dd HH:mm:ss} 不在任何发放时间段内，最近的未来时间段: {nextIssueTime.Issue} ({nextIssueTime.OpenTime:yyyy-MM-dd HH:mm:ss} - {nextIssueTime.CloseTime:yyyy-MM-dd HH:mm:ss})");
            return nextIssueTime;
        }

        // 如果没有找到未来的时间段，可能是当前年份的数据已经全部过期
        Debug.WriteLine($"当前时间 {now:yyyy-MM-dd HH:mm:ss} 没有找到对应的发放时间段，可能需要生成新的年度数据");
        return null;
    }
}