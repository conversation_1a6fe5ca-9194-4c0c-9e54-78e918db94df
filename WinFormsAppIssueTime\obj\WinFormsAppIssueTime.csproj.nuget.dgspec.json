{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\SolutionTemp\\WinFormsAppIssueTime\\WinFormsAppIssueTime.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\SolutionTemp\\WinFormsAppIssueTime\\WinFormsAppIssueTime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\SolutionTemp\\WinFormsAppIssueTime\\WinFormsAppIssueTime.csproj", "projectName": "WinFormsAppIssueTime", "projectPath": "C:\\Users\\<USER>\\Desktop\\SolutionTemp\\WinFormsAppIssueTime\\WinFormsAppIssueTime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\SolutionTemp\\WinFormsAppIssueTime\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Costura.Fody": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "FreeSql": {"target": "Package", "version": "[3.5.211, )"}, "FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.211, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.3.0, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[7.0.1, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}