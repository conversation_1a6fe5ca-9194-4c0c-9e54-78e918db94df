using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using WinFormsAppIssueTime.Interfaces;
using WinFormsAppIssueTime.Services;

namespace WinFormsAppIssueTime;

static class Program
{
    /// <summary>
    /// 应用程序的主入口点
    /// </summary>
    [STAThread]
    static void Main()
    {
        try
        {
            // 初始化日志配置
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File(
                    path: "Logs/.log", // 文件路径（相对或绝对路径）
                    rollingInterval: RollingInterval.Day, // 滚动间隔：Day/Hour/Minute等
                    retainedFileCountLimit: 7, // 保留的日志文件数量
                    fileSizeLimitBytes: 1024 * 1024 * 10, // 单个文件大小限制(10MB)
                    rollOnFileSizeLimit: true, // 达到大小限制时创建新文件
                    shared: true, // 允许多进程共享日志文件
                    outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                .CreateLogger();

            // 自定义应用程序配置，如设置高DPI设置或默认字体
            ApplicationConfiguration.Initialize();

            // 构建配置
            Log.Information("构建配置");
            var configuration = BuildConfiguration();

            // 配置依赖注入容器
            Log.Information("配置依赖注入");
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddSerilog());
            ConfigureServices(services, configuration);

            // 使用依赖注入创建主窗体
            Log.Information("启动主程序");
            var serviceProvider = services.BuildServiceProvider();
            var mainForm = serviceProvider.GetRequiredService<FormMain>();
            Application.Run(mainForm);
            Log.Information("运行结束");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
        }
        finally
        {
            Log.Information("关闭日志");
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 构建配置
    /// </summary>
    /// <returns>配置对象</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 注册配置
        services.AddSingleton(configuration);

        // 注册 FreeSql ORM 框架
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        var freeSql = new FreeSql.FreeSqlBuilder()
            .UseConnectionString(FreeSql.DataType.Sqlite, connectionString) // 使用 SQLite 数据库
            .UseAutoSyncStructure(true) // 自动同步实体结构到数据库
            .Build(); // 构建 FreeSql 实例

        services.AddSingleton(freeSql);

        // 注册服务
        services.AddScoped<IIssueTimeService, IssueTimeService>();

        // 注册窗体
        services.AddTransient<FormMain>();
    }
}