using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using WinFormsAppIssueTime.Interfaces;
using WinFormsAppIssueTime.Models;
using WinFormsAppIssueTime.Services;

namespace WinFormsAppIssueTime;

static class Program
{
    /// <summary>
    /// 应用程序的主入口点
    /// </summary>
    [STAThread]
    static void Main()
    {
        try
        {
            // 构建配置（需要先构建配置才能读取 Serilog 设置）
            var configuration = BuildConfiguration();

            // 从配置文件初始化日志配置
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();

            // 自定义应用程序配置，如设置高DPI设置或默认字体
            ApplicationConfiguration.Initialize();

            // 读取应用程序设置
            var appSettings = configuration.GetSection("AppSettings").Get<AppSettings>() ?? new AppSettings();

            // 记录应用程序启动日志
            Log.Information("应用程序启动 - {ApplicationName} v{Version}",
                appSettings.ApplicationName, appSettings.Version);

            // 配置依赖注入容器
            Log.Information("配置依赖注入服务");
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddSerilog());
            ConfigureServices(services, configuration, appSettings);

            // 使用依赖注入创建主窗体
            Log.Information("启动主窗体应用程序");
            var serviceProvider = services.BuildServiceProvider();
            var mainForm = serviceProvider.GetRequiredService<FormMain>();
            Application.Run(mainForm);
            Log.Information("应用程序正常退出");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
        }
        finally
        {
            Log.Information("应用程序关闭，清理日志资源");
            Log.CloseAndFlush();
        }
    }

    /// <summary>
    /// 构建配置
    /// </summary>
    /// <returns>配置对象</returns>
    private static IConfiguration BuildConfiguration()
    {
        return new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置对象</param>
    /// <param name="appSettings">应用程序设置</param>
    private static void ConfigureServices(IServiceCollection services, IConfiguration configuration, AppSettings appSettings)
    {
        // 注册配置
        services.AddSingleton(configuration);
        services.AddSingleton(appSettings);

        // 注册 FreeSql ORM 框架
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        Log.Information("数据库连接字符串: {ConnectionString}", connectionString);

        var freeSql = new FreeSql.FreeSqlBuilder()
            .UseConnectionString(FreeSql.DataType.Sqlite, connectionString) // 使用 SQLite 数据库
            .UseAutoSyncStructure(true) // 自动同步实体结构到数据库
            .Build(); // 构建 FreeSql 实例

        services.AddSingleton(freeSql);
        Log.Information("FreeSql ORM 框架已配置");

        // 注册业务服务
        services.AddScoped<IIssueTimeService, IssueTimeService>();
        Log.Information("业务服务已注册");

        // 注册窗体
        services.AddTransient<FormMain>();
        Log.Information("主窗体已注册");
    }
}