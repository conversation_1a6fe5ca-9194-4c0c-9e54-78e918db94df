﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <UseWindowsForms>true</UseWindowsForms>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Costura.Fody" Version="6.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="FreeSql" Version="3.5.211" />
      <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.211" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7" />
      <PackageReference Include="Serilog" Version="3.1.1" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="7.0.1" />
      <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
      <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
      <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
      <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    </ItemGroup>

    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

</Project>