C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\appsettings.json
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\WinFormsAppIssueTime.exe
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\WinFormsAppIssueTime.deps.json
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\WinFormsAppIssueTime.runtimeconfig.json
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\WinFormsAppIssueTime.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\WinFormsAppIssueTime.pdb
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\bin\Debug\net8.0-windows\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.AssemblyInfo.cs
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.csproj.Fody.CopyLocal.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.csproj.Fody.RuntimeCopyLocal.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinForms.D85B6CA3.Up2Date
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\refint\WinFormsAppIssueTime.dll
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.pdb
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\WinFormsAppIssueTime.genruntimeconfig.cache
C:\Users\<USER>\Desktop\SolutionTemp\WinFormsAppIssueTime\obj\Debug\net8.0-windows\ref\WinFormsAppIssueTime.dll
