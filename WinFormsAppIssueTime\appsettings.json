{"ConnectionStrings": {"DefaultConnection": "Data Source=Demo.db"}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/app-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "shared": true, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppSettings": {"ApplicationName": "WinForms Issue Time Application", "Version": "1.0.0", "LogSettings": {"EnableFileLogging": true, "EnableConsoleLogging": true, "LogDirectory": "Logs", "MaxLogFileSizeMB": 10, "RetainedLogDays": 7}}}