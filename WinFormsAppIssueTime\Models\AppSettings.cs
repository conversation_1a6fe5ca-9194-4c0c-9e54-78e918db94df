namespace WinFormsAppIssueTime.Models;

/// <summary>
/// 应用程序设置配置模型
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string ApplicationName { get; set; } = "WinForms Issue Time Application";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 日志设置
    /// </summary>
    public LogSettings LogSettings { get; set; } = new();
}

/// <summary>
/// 日志设置配置模型
/// </summary>
public class LogSettings
{
    /// <summary>
    /// 是否启用文件日志
    /// </summary>
    public bool EnableFileLogging { get; set; } = true;

    /// <summary>
    /// 是否启用控制台日志
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// 日志目录
    /// </summary>
    public string LogDirectory { get; set; } = "Logs";

    /// <summary>
    /// 最大日志文件大小（MB）
    /// </summary>
    public int MaxLogFileSizeMB { get; set; } = 10;

    /// <summary>
    /// 保留日志天数
    /// </summary>
    public int RetainedLogDays { get; set; } = 7;
}
